import puppeteer from 'puppeteer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function generatePDF() {
  console.log('🚀 Starting PDF generation...');

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const page = await browser.newPage();

  // Set viewport for consistent rendering
  await page.setViewport({
    width: 1200,
    height: 1600,
    deviceScaleFactor: 2
  });

  console.log('📄 Loading CV page...');

  // Navigate to the local development server
  await page.goto('http://localhost:5174', {
    waitUntil: 'networkidle0',
    timeout: 30000
  });

  // Wait for fonts and animations to load
  await new Promise(resolve => setTimeout(resolve, 3000));

  // Inject CSS to ensure print styles are applied
  await page.addStyleTag({
    content: `
      * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
      }

      .cv-container {
        background: white !important;
        padding: 0 !important;
      }

      .cv-container::before {
        display: none !important;
      }

      .cv-page {
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
        border: none !important;
      }

      /* Disable animations for PDF */
      *, *::before, *::after {
        animation-duration: 0s !important;
        animation-delay: 0s !important;
        transition-duration: 0s !important;
        transition-delay: 0s !important;
      }
    `
  });

  console.log('📋 Generating PDF...');

  // Generate PDF with high quality settings
  const pdf = await page.pdf({
    format: 'A4',
    printBackground: true,
    margin: {
      top: '0.5in',
      right: '0.5in',
      bottom: '0.5in',
      left: '0.5in'
    },
    preferCSSPageSize: true,
    displayHeaderFooter: false
  });

  await browser.close();

  // Save PDF to file
  const outputPath = path.join(__dirname, 'Shivek_Garib_CV.pdf');
  fs.writeFileSync(outputPath, pdf);

  console.log('✅ PDF generated successfully!');
  console.log(`📁 Saved as: ${outputPath}`);
  console.log('🎯 Your professional CV is ready for job applications!');
}

// Run the PDF generation
generatePDF().catch(error => {
  console.error('❌ Error generating PDF:', error);
  process.exit(1);
});
