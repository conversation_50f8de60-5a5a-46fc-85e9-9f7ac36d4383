{"version": 3, "file": "Frame.js", "sourceRoot": "", "sources": ["../../../../src/cdp/Frame.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOH,8CAAmE;AAGnE,mDAAyD;AACzD,+CAA6C;AAC7C,qDAA6C;AAC7C,yDAAoD;AACpD,uDAAiD;AAEjD,yDAAiD;AAQjD,mEAA0D;AAE1D,yDAAiD;AACjD,2DAAgE;AAChE,+DAG+B;AAE/B,yCAA8C;AAE9C;;GAEG;IACU,QAAQ;sBAAS,gBAAK;;;;;;;;;iBAAtB,QAAS,SAAQ,WAAK;;;YAkGjC,+JAAe,IAAI,6DAiFlB;YAGD,sMAAe,iBAAiB,6DAoC/B;YAeD,iLAAe,UAAU,6DA8BxB;YAmBD,mMAAM,gBAAgB,6DAerB;YAGD,8NAAM,yBAAyB,6DAY9B;YAGD,uOAAM,4BAA4B,6DAgBjC;YAGD,4MAAe,mBAAmB,6DAMjC;;;QAnVD,IAAI,IADO,mDAAQ,EACZ,EAAE,EAAC;QACV,SAAS,GAAG,KAAK,CAAC;QAClB,OAAO,CAAa;QAEpB,aAAa,CAAe;QAC5B,SAAS,GAAG,EAAE,CAAC;QACf,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;QAE5B,GAAG,CAAS;QACZ,SAAS,CAAU;QACnB,aAAa,CAAgB;QAEtC,MAAM,CAAqB;QAE3B,YACE,YAA0B,EAC1B,OAAe,EACf,aAAiC,EACjC,MAAkB;YAElB,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;YAC/B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YAEtB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG;gBACZ,CAAC,8BAAU,CAAC,EAAE,IAAI,gCAAa,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;gBACzE,CAAC,mCAAe,CAAC,EAAE,IAAI,gCAAa,CAClC,IAAI,EACJ,IAAI,CAAC,aAAa,CAAC,eAAe,CACnC;aACF,CAAC;YAEF,IAAI,CAAC,aAAa,GAAG,IAAI,gCAAa,CAAC,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,EAAE,OAAO,CAAC,CAAC;YAEzE,IAAI,CAAC,EAAE,CAAC,qBAAU,CAAC,wBAAwB,EAAE,GAAG,EAAE;gBAChD,8CAA8C;gBAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,OAAO,CAAC,EAAE,CAChC,kBAAkB,EAClB,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7C,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,OAAO,CAAC,EAAE,CAChC,eAAe,EACf,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1C,CAAC;QACJ,CAAC;QAED,4BAA4B,CAC1B,KAA6C;YAE7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,yCAAiB,CAAC,gBAAgB,EAAE;gBAC1D,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC;gBACvB,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAED,yBAAyB,CAAC,KAA0C;YAClE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,yCAAiB,CAAC,aAAa,EAAE;gBACvD,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC;gBACvB,KAAK;aACN,CAAC,CAAC;QACL,CAAC;QAED;;;;WAIG;QACH,OAAO;YACL,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAED;;;WAGG;QACH,QAAQ,CAAC,EAAU;YACjB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QAChB,CAAC;QAED,YAAY,CAAC,MAAkB;YAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACxB,CAAC;QAEQ,IAAI;YACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC;QAGQ,KAAK,CAAC,IAAI,CACjB,GAAW,EACX,UAKI,EAAE;YAEN,MAAM,EACJ,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,EACzE,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CACnE,gBAAgB,CACjB,EACD,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;YAEZ,IAAI,2BAA2B,GAAG,KAAK,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;YACF,IAAI,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;gBAC9B,QAAQ,CACN,IAAI,CAAC,OAAO,EACZ,GAAG,EACH,OAAO,EACP,cAA8C,EAC9C,IAAI,CAAC,GAAG,CACT;gBACD,OAAO,CAAC,kBAAkB,EAAE;aAC7B,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;oBAC1B,OAAO,CAAC,kBAAkB,EAAE;oBAC5B,2BAA2B;wBACzB,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE;wBACxC,CAAC,CAAC,OAAO,CAAC,6BAA6B,EAAE;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC;gBACH,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC5C,CAAC;oBAAS,CAAC;gBACT,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;YAED,KAAK,UAAU,QAAQ,CACrB,MAAkB,EAClB,GAAW,EACX,QAA4B,EAC5B,cAAwD,EACxD,OAAe;gBAEf,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;wBAClD,GAAG;wBACH,QAAQ;wBACR,OAAO;wBACP,cAAc;qBACf,CAAC,CAAC;oBACH,2BAA2B,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAClD,IAAI,QAAQ,CAAC,SAAS,KAAK,qCAAqC,EAAE,CAAC;wBACjE,OAAO,IAAI,CAAC;oBACd,CAAC;oBACD,OAAO,QAAQ,CAAC,SAAS;wBACvB,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,OAAO,GAAG,EAAE,CAAC;wBAC9C,CAAC,CAAC,IAAI,CAAC;gBACX,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,EAAE,CAAC;wBACvB,OAAO,KAAK,CAAC;oBACf,CAAC;oBACD,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAGQ,KAAK,CAAC,iBAAiB,CAC9B,UAA0B,EAAE;YAE5B,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,EAChE,MAAM,GACP,GAAG,OAAO,CAAC;YACZ,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,IAAI,EACJ,SAAS,EACT,OAAO,EACP,MAAM,CACP,CAAC;YACF,MAAM,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;gBAChC,OAAO,CAAC,kBAAkB,EAAE;gBAC5B,GAAG,CAAC,OAAO,CAAC,4BAA4B;oBACtC,CAAC,CAAC,EAAE;oBACJ,CAAC,CAAC,CAAC,OAAO,CAAC,6BAA6B,EAAE,CAAC,CAAC;gBAC9C,OAAO,CAAC,4BAA4B,EAAE;aACvC,CAAC,CAAC;YACH,IAAI,CAAC;gBACH,IAAI,KAAK,EAAE,CAAC;oBACV,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,MAAM,MAAM,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAEhC,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;gBAChE,IAAI,MAAM,YAAY,KAAK,EAAE,CAAC;oBAC5B,MAAM,KAAK,CAAC;gBACd,CAAC;gBACD,OAAO,MAAM,IAAI,IAAI,CAAC;YACxB,CAAC;oBAAS,CAAC;gBACT,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;QAED,IAAa,MAAM;YACjB,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAEQ,SAAS;YAChB,OAAO,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC;QACjC,CAAC;QAEQ,aAAa;YACpB,OAAO,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC;QACtC,CAAC;QAGQ,KAAK,CAAC,UAAU,CACvB,IAAY,EACZ,UAGI,EAAE;YAEN,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;YAEZ,oFAAoF;YACpF,iDAAiD;YACjD,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEjC,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;YACF,MAAM,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAA2B;gBAC1D,OAAO,CAAC,kBAAkB,EAAE;gBAC5B,OAAO,CAAC,gBAAgB,EAAE;aAC3B,CAAC,CAAC;YACH,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAEQ,GAAG;YACV,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QAEQ,WAAW;YAClB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;QACrE,CAAC;QAEQ,WAAW;YAClB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7D,CAAC;QAED,2BAA2B;YACzB,OAAO,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtE,CAAC;QAGD,KAAK,CAAC,gBAAgB,CAAC,aAA+B;YACpD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,WAAW,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,CAAC,MAAM,EAAE,CAAC;gBACvD,OAAO;YACT,CAAC;YACD,IAAI,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,OAAO;YACT,CAAC;YACD,MAAM,EAAC,UAAU,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAC1C,uCAAuC,EACvC;gBACE,MAAM,EAAE,aAAa,CAAC,MAAM;aAC7B,CACF,CAAC;YACF,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAChD,CAAC;QAGD,KAAK,CAAC,yBAAyB,CAAC,OAAgB;YAC9C,oEAAoE;YACpE,iDAAiD;YACjD,IAAI,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACxE,OAAO;YACT,CAAC;YACD,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;oBACtC,IAAI,EAAE,6BAAkB,GAAG,OAAO,CAAC,IAAI;iBACxC,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oBAAU,CAAC;aACpD,CAAC,CAAC;QACL,CAAC;QAGD,KAAK,CAAC,4BAA4B,CAAC,OAAgB;YACjD,oEAAoE;YACpE,iDAAiD;YACjD,IAAI,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACxE,OAAO;YACT,CAAC;YACD,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBACzC,IAAI,EAAE,6BAAkB,GAAG,OAAO,CAAC,IAAI;iBACxC,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBACnB,kDAAkD;oBAClD,4CAA4C;oBAC5C,UAAU,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;gBAC/B,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,oBAAU,CAAC;aACnC,CAAC,CAAC;QACL,CAAC;QAGQ,KAAK,CAAC,mBAAmB,CAChC,UAA8B,EAAE;YAEhC,OAAO,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC,mBAAmB,CACjE,OAAO,CACR,CAAC;QACJ,CAAC;QAED,UAAU,CAAC,YAAiC;YAC1C,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;YAC/B,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;QACrE,CAAC;QAED,wBAAwB,CAAC,GAAW;YAClC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAClB,CAAC;QAED,iBAAiB,CAAC,QAAgB,EAAE,IAAY;YAC9C,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;QAED,iBAAiB;YACf,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAC9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,iBAAiB;YACf,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAa,QAAQ;YACnB,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAEQ,sBAnRR,0BAAe,oCAoFf,0BAAe,6BAmDf,0BAAe,mCAiDf,0BAAe,4CAkBf,0BAAe,+CAef,0BAAe,sCAmBf,0BAAe,GAuCN,6BAAa,EAAC;YACtB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,6BAAa,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC,6BAAa,CAAC,EAAE,CAAC;QAChD,CAAC;QAED,cAAc;YACZ,MAAM,IAAI,gCAAoB,EAAE,CAAC;QACnC,CAAC;QAEQ,KAAK,CAAC,YAAY;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,EAAC,aAAa,EAAC,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACpE,OAAO,EAAE,IAAI,CAAC,GAAG;aAClB,CAAC,CAAC;YACH,OAAO,CAAC,MAAM,MAAM;iBACjB,SAAS,EAAE;iBACX,gBAAgB,CAAC,aAAa,CAAC,CAAqC,CAAC;QAC1E,CAAC;;;AA5YU,4BAAQ"}