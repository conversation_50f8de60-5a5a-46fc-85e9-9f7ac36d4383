{"version": 3, "file": "CdpSession.js", "sourceRoot": "", "sources": ["../../../../src/cdp/CdpSession.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAIH,wDAK8B;AAC9B,uEAA+D;AAC/D,mDAAqD;AACrD,iDAAyC;AACzC,uDAAgE;AAKhE;;GAEG;AAEH,MAAa,aAAc,SAAQ,0BAAU;IAC3C,UAAU,CAAS;IACnB,WAAW,CAAS;IACpB,UAAU,GAAG,IAAI,sCAAgB,EAAE,CAAC;IACpC,WAAW,CAAa;IACxB,gBAAgB,CAAU;IAC1B,OAAO,CAAa;IACpB,UAAU,GAAG,KAAK,CAAC;IACnB,SAAS,GAAG,KAAK,CAAC;IAClB;;OAEG;IACH,YACE,UAAsB,EACtB,UAAkB,EAClB,SAAiB,EACjB,eAAmC,EACnC,SAAkB;QAElB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,MAAiB;QACzB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,MAAM;QACJ,IAAA,kBAAM,EAAC,IAAI,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,IAAa,QAAQ;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC;IACpD,CAAC;IAEQ,aAAa;QACpB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,0EAA0E;YAC1E,4DAA4D;YAC5D,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAChE,OAAO,MAAM,IAAI,SAAS,CAAC;IAC7B,CAAC;IAEQ,IAAI,CACX,MAAS,EACT,MAAqD,EACrD,OAAwB;QAExB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,4BAAgB,CAClB,mBAAmB,MAAM,sCAAsC,IAAI,CAAC,WAAW,mBAAmB,CACnG,CACF,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAC9B,IAAI,CAAC,UAAU,EACf,MAAM,EACN,MAAM,EACN,IAAI,CAAC,UAAU,EACf,OAAO,CACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,MAMT;QACC,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACd,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBACpB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBACrD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,UAAU,CAAC,MAAM,CACpB,MAAM,CAAC,EAAE,EACT,IAAA,yCAA0B,EAAC,MAAM,CAAC,EAClC,MAAM,CAAC,KAAK,CAAC,OAAO,CACrB,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAA,kBAAM,EAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;;OAGG;IACM,KAAK,CAAC,MAAM;QACnB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,6CAA6C,IAAI,CAAC,WAAW,mBAAmB,CACjF,CAAC;QACJ,CAAC;QACD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACrD,SAAS,EAAE,IAAI,CAAC,UAAU;SAC3B,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,+BAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACM,EAAE;QACT,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,CAAC;IACpD,CAAC;CACF;AAzJD,sCAyJC"}