{"version": 3, "file": "puppeteer-core.js", "sourceRoot": "", "sources": ["../../../src/puppeteer-core.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,cAAc,YAAY,CAAC;AAE3B,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,IAAI,MAAM,WAAW,CAAC;AAE7B,OAAO,EAAC,WAAW,EAAC,MAAM,kBAAkB,CAAC;AAE7C,OAAO,KAAK,SAAS,MAAM,YAAY,CAAC;AAExC,iDAAiD;AACjD,WAAW,CAAC,KAAK,GAAG;IAClB,EAAE;IACF,IAAI;IACJ,cAAc,EAAE,SAAS,CAAC,cAAc;CACzC,CAAC;AACF;;GAEG;AACH,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,aAAa,CAAC;IAC5C,eAAe,EAAE,IAAI;CACtB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM;AACX;;GAEG;AACH,OAAO;AACP;;GAEG;AACH,WAAW;AACX;;GAEG;AACH,cAAc;AACd;;GAEG;AACH,MAAM,GACP,GAAG,SAAS,CAAC;AAEd,eAAe,SAAS,CAAC"}