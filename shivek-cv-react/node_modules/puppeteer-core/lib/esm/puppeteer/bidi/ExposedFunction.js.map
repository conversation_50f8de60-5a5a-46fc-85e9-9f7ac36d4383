{"version": 3, "file": "ExposedFunction.js", "sourceRoot": "", "sources": ["../../../../src/bidi/ExposedFunction.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAEnE,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AAEvD,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAC,mBAAmB,EAAE,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAG3E,OAAO,EAAC,iBAAiB,EAAC,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAC,YAAY,EAAC,MAAM,eAAe,CAAC;AAU3C;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAC5B,MAAM,CAAC,KAAK,CAAC,IAAI,CACf,KAAgB,EAChB,IAAY,EACZ,KAAwC,EACxC,OAAO,GAAG,KAAK;QAEf,MAAM,IAAI,GAAG,IAAI,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAChE,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAEQ,MAAM,CAAC;IAEP,IAAI,CAAC;IACL,MAAM,CAAC;IACP,QAAQ,CAAC;IAET,QAAQ,CAAC;IAElB,QAAQ,GAAkD,EAAE,CAAC;IAC7D,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;IAErC,YACE,KAAgB,EAChB,IAAY,EACZ,KAAwC,EACxC,OAAO,GAAG,KAAK;QAEf,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,wBAAwB,IAAI,CAAC,IAAI,EAAE,CAAC;IACrF,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,SAAkB;YACxB,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,SAAS,+CAAkC;aAC5C;SACF,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC7C,IAAI,YAAY,CAAC,UAAU,CAAC,CAC7B,CAAC;QACF,iBAAiB,CAAC,EAAE,CAClB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAC3C,IAAI,CAAC,cAAc,CACpB,CAAC;QAEF,MAAM,mBAAmB,GAAG,iBAAiB,CAC3C,mBAAmB,CACjB,CAAC,QAAoC,EAAE,EAAE;YACvC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBACxB,CAAC,WAAW,CAAC,MAAM,CAAW,CAAC,EAAE,UAAU,GAAG,IAAU;oBACtD,OAAO,IAAI,OAAO,CAChB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;wBAClB,QAAQ,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;oBACpC,CAAC,CACF,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAC,CAClC,CACF,CAAC;QAEF,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACxE,IAAI,CAAC;gBACH,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;oBACjC,KAAK,CAAC,eAAe,CAAC,gBAAgB,CAAC,mBAAmB,EAAE;wBAC1D,SAAS,EAAE,CAAC,OAAO,CAAC;wBACpB,OAAO,EAAE,KAAK,CAAC,OAAO;qBACvB,CAAC;oBACF,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE;wBACnD,SAAS,EAAE,CAAC,OAAO,CAAC;qBACrB,CAAC;iBACH,CAAC,CAAC;gBACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,qEAAqE;gBACrE,mBAAmB;gBACnB,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IACjD,CAAC;IAED,cAAc,GAAG,KAAK,EAAE,MAAqC,EAAE,EAAE;;;YAC/D,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC5C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,qBAAqB;gBACrB,OAAO;YACT,CAAC;YAED,MAAM,UAAU,kCAAG,YAAY,CAAC,IAAI,CAMlC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAA,CAAC;YAEtB,MAAM,KAAK,kCAAG,IAAI,eAAe,EAAE,QAAA,CAAC;YACpC,MAAM,IAAI,GAAG,EAAE,CAAC;YAEhB,IAAI,MAAM,CAAC;YACX,IAAI,CAAC;;;oBACH,MAAM,UAAU,kCAAG,MAAM,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,IAAI,CAAC,EAAE,EAAE;wBAChE,OAAO,IAAI,CAAC;oBACd,CAAC,CAAC,QAAA,CAAC;oBAEH,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,MAAM,UAAU,CAAC,aAAa,EAAE,EAAE,CAAC;wBAC/D,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;wBAElB,oCAAoC;wBACpC,IAAI,MAAM,YAAY,iBAAiB,EAAE,CAAC;4BACxC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;4BACtB,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;4BAClB,SAAS;wBACX,CAAC;wBAED,6CAA6C;wBAC7C,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;oBACpC,CAAC;oBACD,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAI,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAU,CAAC,CAAC;;;;;;;;;aACpE;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC;oBACH,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;wBAC3B,MAAM,UAAU,CAAC,QAAQ,CACvB,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE;4BACnC,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;4BACjC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;4BAClB,IAAI,KAAK,EAAE,CAAC;gCACV,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;4BACtB,CAAC;4BACD,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC,EACD,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,KAAK,CACZ,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE;4BAC9C,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC,EAAE,KAAK,CAAC,CAAC;oBACZ,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,UAAU,CAAC,KAAK,CAAC,CAAC;gBACpB,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,EAAE;oBAC9C,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC,EAAE,MAAM,CAAC,CAAC;YACb,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;;;;;;;;;KACF,CAAC;IAEF,SAAS,CAAC,MAA0B;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAiB,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,qBAAqB;YACrB,OAAO;QACT,CAAC;QACD,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,UAAU,CAAC,EAAU;QACnB,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;QACtC,CAAC;QACD,OAAO;IACT,CAAC;IAED,CAAC,MAAM,CAAC,OAAO,CAAC;QACd,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC5B,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE;YAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACxE,IAAI,CAAC;gBACH,MAAM,OAAO,CAAC,GAAG,CAAC;oBAChB,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;wBACpB,OAAQ,UAAkB,CAAC,IAAI,CAAC,CAAC;oBACnC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;oBACb,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;wBACtC,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;4BAChC,OAAQ,UAAkB,CAAC,IAAI,CAAC,CAAC;wBACnC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBAChB,CAAC,CAAC;oBACF,KAAK,CAAC,eAAe,CAAC,mBAAmB,CAAC,MAAM,CAAC;iBAClD,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF"}