{"version": 3, "file": "BrowserConnector.js", "sourceRoot": "", "sources": ["../../../../src/common/BrowserConnector.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAC,qBAAqB,EAAC,MAAM,6BAA6B,CAAC;AAClE,OAAO,EAAC,oBAAoB,EAAC,MAAM,4BAA4B,CAAC;AAChE,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AAKjD,MAAM,0BAA0B,GAAG,KAAK,IAAI,EAAE;IAC5C,OAAO,MAAM;QACX,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC,CAAC,sBAAsB;QAC5E,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,wCAAwC,CAAC,CAAC;aACrD,yBAAyB,CAAC;AACnC,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAuB;IAEvB,MAAM,EAAC,mBAAmB,EAAE,WAAW,EAAC,GACtC,MAAM,sBAAsB,CAAC,OAAO,CAAC,CAAC;IAExC,IAAI,OAAO,CAAC,QAAQ,KAAK,eAAe,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,MAAM,qBAAqB,CAC7C,mBAAmB,EACnB,WAAW,EACX,OAAO,CACR,CAAC;QACF,OAAO,WAAW,CAAC;IACrB,CAAC;SAAM,CAAC;QACN,MAAM,UAAU,GAAG,MAAM,oBAAoB,CAC3C,mBAAmB,EACnB,WAAW,EACX,OAAO,CACR,CAAC;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,sBAAsB,CACnC,OAAuB;IAEvB,MAAM,EAAC,iBAAiB,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,GAAG,EAAE,EAAC,GAAG,OAAO,CAAC;IAEzE,MAAM,CACJ,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QACtE,CAAC,EACH,+FAA+F,CAChG,CAAC;IAEF,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,EAAC,mBAAmB,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE,EAAC,CAAC;IAC3D,CAAC;SAAM,IAAI,iBAAiB,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAG,MAAM,0BAA0B,EAAE,CAAC;QAC1D,MAAM,mBAAmB,GACvB,MAAM,cAAc,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC1D,OAAO;YACL,mBAAmB,EAAE,mBAAmB;YACxC,WAAW,EAAE,iBAAiB;SAC/B,CAAC;IACJ,CAAC;SAAM,IAAI,UAAU,EAAE,CAAC;QACtB,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC,CAAC;QACtD,MAAM,cAAc,GAAG,MAAM,0BAA0B,EAAE,CAAC;QAC1D,MAAM,mBAAmB,GACvB,MAAM,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC7C,OAAO;YACL,mBAAmB,EAAE,mBAAmB;YACxC,WAAW,EAAE,aAAa;SAC3B,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAChD,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,UAAkB;IAC7C,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;IAEzD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE;YAC5D,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,QAAQ,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QAC/C,CAAC;QACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,OAAO;gBACX,8CAA8C,WAAW,IAAI;oBAC7D,KAAK,CAAC,OAAO,CAAC;QAClB,CAAC;QACD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC"}