{"version": 3, "file": "PSelectorParser.js", "sourceRoot": "", "sources": ["../../../../src/common/PSelectorParser.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAEL,QAAQ,EACR,MAAM,EACN,SAAS,GACV,MAAM,0CAA0C,CAAC;AAQlD,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AACzB,MAAM,CAAC,YAAY,CAAC,GAAG,wBAAwB,CAAC;AAEhD,MAAM,aAAa,GAAG,WAAW,CAAC;AAClC,MAAM,OAAO,GAAG,CAAC,IAAY,EAAU,EAAE;IACvC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;QACzC,OAAO,KAAK,CAAC,CAAC,CAAW,CAAC;IAC5B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,QAAgB;IAOhB,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAClC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IACD,IAAI,gBAAgB,GAAsB,EAAE,CAAC;IAC7C,IAAI,eAAe,GAAqB,CAAC,gBAAgB,CAAC,CAAC;IAC3D,MAAM,SAAS,GAAyB,CAAC,eAAe,CAAC,CAAC;IAC1D,MAAM,OAAO,GAAY,EAAE,CAAC;IAC5B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,YAAY;gBACf,QAAQ,KAAK,CAAC,OAAO,EAAE,CAAC;oBACtB;wBACE,SAAS,GAAG,KAAK,CAAC;wBAClB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;4BACnB,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;4BAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBACpB,CAAC;wBACD,gBAAgB,GAAG,EAAE,CAAC;wBACtB,eAAe,CAAC,IAAI,oCAAwB,CAAC;wBAC7C,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACvC,SAAS;oBACX;wBACE,SAAS,GAAG,KAAK,CAAC;wBAClB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;4BACnB,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;4BAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBACpB,CAAC;wBACD,gBAAgB,GAAG,EAAE,CAAC;wBACtB,eAAe,CAAC,IAAI,gCAAmB,CAAC;wBACxC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACvC,SAAS;gBACb,CAAC;gBACD,MAAM;YACR,KAAK,gBAAgB;gBACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBAClC,MAAM;gBACR,CAAC;gBACD,SAAS,GAAG,KAAK,CAAC;gBAClB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnB,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC;gBACD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;oBACpB,OAAO,GAAG,IAAI,CAAC;gBACjB,CAAC;gBACD,gBAAgB,CAAC,IAAI,CAAC;oBACpB,IAAI;oBACJ,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC;iBACrC,CAAC,CAAC;gBACH,SAAS;YACX,KAAK,cAAc;gBACjB,gBAAgB,GAAG,IAAI,CAAC;gBACxB,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnB,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC;gBACD,gBAAgB,GAAG,EAAE,CAAC;gBACtB,eAAe,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBACrC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAChC,SAAS;QACb,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC"}