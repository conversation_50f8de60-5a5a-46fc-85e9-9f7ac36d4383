{"version": 3, "file": "Poller.js", "sourceRoot": "", "sources": ["../../../../src/injected/Poller.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAW7C;;GAEG;AACH,MAAM,OAAO,cAAc;IACzB,GAAG,CAAmB;IAEtB,KAAK,CAAO;IAEZ,SAAS,CAAoB;IAC7B,SAAS,CAAe;IACxB,YAAY,EAAoB,EAAE,IAAU;QAC1C,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAK,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,MAAM,EAAE,CAAC;YACX,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,KAAK,IAAI,EAAE;YAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YACD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;YACjC,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,MAAM;QACJ,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,SAAS;IACpB,GAAG,CAAmB;IACtB,SAAS,CAAe;IACxB,YAAY,EAAoB;QAC9B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAK,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,MAAM,EAAE,CAAC;YACX,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzB,OAAO;QACT,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,IAAI,EAAE;YACtB,IAAI,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACxB,OAAO;YACT,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBACnC,OAAO;YACT,CAAC;YACD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,CAAC;QACF,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,MAAM;QACJ,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC;CACF;AAED;;GAEG;AAEH,MAAM,OAAO,cAAc;IACzB,GAAG,CAAmB;IACtB,GAAG,CAAS;IAEZ,SAAS,CAAkB;IAC3B,SAAS,CAAe;IACxB,YAAY,EAAoB,EAAE,EAAU;QAC1C,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAK,CAAC,CAAC;QACzD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,MAAM,EAAE,CAAC;YACX,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YACD,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,MAAM;QACJ,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC;CACF"}