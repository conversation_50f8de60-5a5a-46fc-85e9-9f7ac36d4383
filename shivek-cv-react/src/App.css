/* Psychological CV Styling for Maximum Impact */
.cv-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  position: relative;
}

.cv-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.cv-page {
  max-width: 210mm; /* A4 width */
  margin: 0 auto;
  background: white;
  box-shadow: var(--shadow-xl), 0 0 0 1px rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out;
  position: relative;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.cv-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  z-index: 10;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Psychological Section Styling */
.cv-section {
  padding: 2.5rem;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.cv-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: var(--primary-gradient);
  transition: width 0.4s ease;
  z-index: 1;
}

.cv-section:hover::before {
  width: 4px;
}

.cv-section:hover {
  background: linear-gradient(135deg, var(--bg-accent) 0%, rgba(255, 255, 255, 0.9) 100%);
  transform: translateX(8px);
  box-shadow: var(--shadow-lg);
}

.cv-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 800;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 0.75rem;
  letter-spacing: -0.025em;
  text-transform: uppercase;
  font-size: 1.25rem;
  position: relative;
  z-index: 2;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: 2px;
  box-shadow: var(--shadow-md);
}

/* Bottom sections layout */
.bottom-sections {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0;
}

.bottom-sections .cv-section {
  border-right: 1px solid #e5e7eb;
  border-bottom: none;
}

.bottom-sections .cv-section:last-child {
  border-right: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cv-container {
    padding: 1rem;
  }

  .cv-page {
    border-radius: 8px;
  }

  .cv-section {
    padding: 1.5rem;
  }

  .bottom-sections {
    grid-template-columns: 1fr;
  }

  .bottom-sections .cv-section {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .bottom-sections .cv-section:last-child {
    border-bottom: none;
  }
}

/* Print Styles */
@media print {
  .cv-container {
    background: white;
    padding: 0;
  }

  .cv-page {
    box-shadow: none;
    border-radius: 0;
    max-width: none;
  }

  .cv-section:hover {
    background-color: white;
  }
}
