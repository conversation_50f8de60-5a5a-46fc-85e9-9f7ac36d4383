/* Modern CV Styling */
.cv-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.cv-page {
  max-width: 210mm; /* A4 width */
  margin: 0 auto;
  background: white;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Section Styling */
.cv-section {
  padding: 2rem;
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.cv-section:hover {
  background-color: #f8fafc;
}

.cv-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

/* Bottom sections layout */
.bottom-sections {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0;
}

.bottom-sections .cv-section {
  border-right: 1px solid #e5e7eb;
  border-bottom: none;
}

.bottom-sections .cv-section:last-child {
  border-right: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cv-container {
    padding: 1rem;
  }

  .cv-page {
    border-radius: 8px;
  }

  .cv-section {
    padding: 1.5rem;
  }

  .bottom-sections {
    grid-template-columns: 1fr;
  }

  .bottom-sections .cv-section {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .bottom-sections .cv-section:last-child {
    border-bottom: none;
  }
}

/* Print Styles */
@media print {
  .cv-container {
    background: white;
    padding: 0;
  }

  .cv-page {
    box-shadow: none;
    border-radius: 0;
    max-width: none;
  }

  .cv-section:hover {
    background-color: white;
  }
}
