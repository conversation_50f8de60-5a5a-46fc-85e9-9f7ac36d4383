.references-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reference-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.reference-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.reference-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  flex-shrink: 0;
}

.reference-details {
  flex: 1;
}

.reference-name {
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.reference-company {
  font-size: 0.875rem;
  color: #4b5563;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.reference-contact {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.contact-phone,
.contact-email {
  font-size: 0.75rem;
  color: #6b7280;
}

@media (max-width: 768px) {
  .reference-item {
    padding: 0.75rem;
  }
}
