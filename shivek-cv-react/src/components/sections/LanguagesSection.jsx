import React from 'react'
import './LanguagesSection.css'

const LanguagesSection = ({ data }) => {
  const languageFlags = {
    'English': '🇬🇧',
    'Hindi': '🇮🇳',
    'Afrikaans': '🇿🇦'
  }

  return (
    <div className="cv-section languages-section">
      <h2 className="section-title">Languages</h2>
      <div className="languages-list">
        {data.map((language, index) => (
          <div key={index} className="language-item">
            <span className="language-flag">{languageFlags[language] || '🌍'}</span>
            <span className="language-name">{language}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

export default LanguagesSection
