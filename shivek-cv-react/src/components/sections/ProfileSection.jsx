import React from 'react'
import './ProfileSection.css'

const ProfileSection = ({ data }) => {
  return (
    <div className="cv-section profile-section">
      <div className="profile-header">
        <div className="profile-info">
          <h1 className="profile-name">{data.name}</h1>
          <h2 className="profile-title">{data.title}</h2>
          <p className="profile-summary">{data.summary}</p>
        </div>
        <div className="profile-avatar">
          <div className="avatar-placeholder">
            <span className="avatar-initials">
              {data.name.split(' ').map(n => n[0]).join('')}
            </span>
          </div>
        </div>
      </div>
      
      <div className="contact-grid">
        <div className="contact-item">
          <div className="contact-icon">📧</div>
          <div className="contact-details">
            <span className="contact-label">Email</span>
            <span className="contact-value">{data.email}</span>
          </div>
        </div>
        
        <div className="contact-item">
          <div className="contact-icon">📱</div>
          <div className="contact-details">
            <span className="contact-label">Phone</span>
            <span className="contact-value">{data.phone}</span>
          </div>
        </div>
        
        <div className="contact-item">
          <div className="contact-icon">📍</div>
          <div className="contact-details">
            <span className="contact-label">Address</span>
            <span className="contact-value">{data.address}</span>
          </div>
        </div>
        
        <div className="contact-item">
          <div className="contact-icon">🎂</div>
          <div className="contact-details">
            <span className="contact-label">Date of Birth</span>
            <span className="contact-value">{data.dateOfBirth}</span>
          </div>
        </div>
        
        <div className="contact-item">
          <div className="contact-icon">🌍</div>
          <div className="contact-details">
            <span className="contact-label">Nationality</span>
            <span className="contact-value">{data.nationality}</span>
          </div>
        </div>
        
        <div className="contact-item">
          <div className="contact-icon">💼</div>
          <div className="contact-details">
            <span className="contact-label">LinkedIn</span>
            <span className="contact-value">{data.linkedin}</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfileSection
