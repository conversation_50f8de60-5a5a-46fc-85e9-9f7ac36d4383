import React from 'react'
import './ExperienceSection.css'

const ExperienceSection = ({ data }) => {
  return (
    <div className="cv-section experience-section">
      <h2 className="section-title">Experience</h2>
      <div className="experience-timeline">
        {data.map((experience, index) => (
          <div key={index} className="experience-item">
            <div className="experience-marker">
              <div className="marker-dot"></div>
              <div className="marker-line"></div>
            </div>
            <div className="experience-content">
              <div className="experience-header">
                <div className="experience-title-group">
                  <h3 className="experience-title">{experience.title}</h3>
                  <div className="experience-company">
                    <span className="company-icon">🏢</span>
                    {experience.company}
                  </div>
                </div>
                <div className="experience-period">
                  <span className="period-icon">📅</span>
                  {experience.period}
                </div>
              </div>
              <div className="experience-responsibilities">
                {experience.responsibilities.map((responsibility, respIndex) => (
                  <div key={respIndex} className="responsibility-item">
                    <span className="responsibility-bullet">▸</span>
                    <span className="responsibility-text">{responsibility}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default ExperienceSection
