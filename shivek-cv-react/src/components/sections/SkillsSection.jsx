import React from 'react'
import './SkillsSection.css'

const SkillsSection = ({ data }) => {
  const skillCategories = [
    { title: 'Programming', skills: data.programming, icon: '💻' },
    { title: 'Web Technologies', skills: data.web, icon: '🌐' },
    { title: 'Databases', skills: data.databases, icon: '🗄️' },
    { title: 'AI & Machine Learning', skills: data.ai, icon: '🤖' },
    { title: 'Tools', skills: data.tools, icon: '🛠️' },
    { title: 'Soft Skills', skills: data.softSkills, icon: '🎯' }
  ]

  return (
    <div className="cv-section skills-section">
      <h2 className="section-title">Skills</h2>
      <div className="skills-grid">
        {skillCategories.map((category, index) => (
          <div key={index} className="skill-category">
            <div className="category-header">
              <span className="category-icon">{category.icon}</span>
              <h3 className="category-title">{category.title}</h3>
            </div>
            <div className="skills-list">
              {category.skills.map((skill, skillIndex) => (
                <span key={skillIndex} className="skill-tag">
                  {skill}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default SkillsSection
