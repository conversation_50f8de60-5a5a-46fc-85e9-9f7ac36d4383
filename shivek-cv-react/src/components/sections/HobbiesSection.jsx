import React from 'react'
import './HobbiesSection.css'

const HobbiesSection = ({ data }) => {
  const hobbyIcons = {
    'Coding': '💻',
    'Hacking': '🔐',
    'Calisthenics': '💪',
    'Football': '⚽'
  }

  return (
    <div className="cv-section hobbies-section">
      <h2 className="section-title">Hobbies</h2>
      <div className="hobbies-list">
        {data.map((hobby, index) => (
          <div key={index} className="hobby-item">
            <span className="hobby-icon">{hobbyIcons[hobby] || '🎯'}</span>
            <span className="hobby-name">{hobby}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

export default HobbiesSection
