import React from 'react'
import './ReferencesSection.css'

const ReferencesSection = ({ data }) => {
  return (
    <div className="cv-section references-section">
      <h2 className="section-title">References</h2>
      <div className="references-list">
        {data.map((reference, index) => (
          <div key={index} className="reference-item">
            <div className="reference-icon">👤</div>
            <div className="reference-details">
              <h3 className="reference-name">{reference.name}</h3>
              <p className="reference-company">{reference.company}</p>
              <div className="reference-contact">
                <span className="contact-phone">📞 {reference.phone}</span>
                {reference.email && (
                  <span className="contact-email">✉️ {reference.email}</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default ReferencesSection
