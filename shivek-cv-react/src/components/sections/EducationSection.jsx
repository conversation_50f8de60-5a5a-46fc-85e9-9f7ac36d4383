import React from 'react'
import './EducationSection.css'

const EducationSection = ({ data }) => {
  return (
    <div className="cv-section education-section">
      <h2 className="section-title">Education</h2>
      <div className="education-timeline">
        {data.map((education, index) => (
          <div key={index} className="education-item">
            <div className="education-marker">
              <div className="marker-dot"></div>
              <div className="marker-line"></div>
            </div>
            <div className="education-content">
              <div className="education-header">
                <h3 className="education-degree">{education.degree}</h3>
                {education.honors && (
                  <span className="education-honors">{education.honors}</span>
                )}
              </div>
              <div className="education-details">
                <div className="education-institution">
                  <span className="institution-icon">🎓</span>
                  {education.institution}
                </div>
                <div className="education-period">
                  <span className="period-icon">📅</span>
                  {education.period}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default EducationSection
