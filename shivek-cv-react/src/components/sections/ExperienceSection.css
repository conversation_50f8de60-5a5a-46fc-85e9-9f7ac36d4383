.experience-timeline {
  position: relative;
  padding-left: 2rem;
}

.experience-item {
  position: relative;
  margin-bottom: 2.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.experience-item:last-child {
  margin-bottom: 0;
}

.experience-item:last-child .marker-line {
  display: none;
}

.experience-marker {
  position: relative;
  flex-shrink: 0;
}

.experience-marker .marker-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: 4px solid white;
  box-shadow: 0 0 0 3px #e5e7eb, 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 2;
}

.experience-marker .marker-line {
  position: absolute;
  top: 18px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 80px;
  background: #e5e7eb;
  z-index: 1;
}

.experience-content {
  flex: 1;
  background: #f8fafc;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.experience-content:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.experience-title-group {
  flex: 1;
}

.experience-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.experience-company {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #4b5563;
}

.company-icon {
  font-size: 1.2rem;
}

.experience-period {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.period-icon {
  font-size: 1rem;
}

.experience-responsibilities {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.responsibility-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.responsibility-bullet {
  color: #667eea;
  font-weight: 700;
  font-size: 1.1rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.responsibility-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #374151;
}

@media (max-width: 768px) {
  .experience-timeline {
    padding-left: 1rem;
  }
  
  .experience-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .experience-period {
    align-self: flex-start;
  }
  
  .experience-content {
    padding: 1.5rem;
  }
}
