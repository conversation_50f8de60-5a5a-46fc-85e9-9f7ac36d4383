.certifications-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.certification-item {
  display: flex;
  align-items: center;
  gap: 2rem;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.9) 100%);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid var(--border-color);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.certification-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left 0.6s ease;
}

.certification-item:hover::before {
  left: 100%;
}

.certification-item:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-success);
  border-color: var(--border-accent);
  background: linear-gradient(135deg, var(--bg-accent) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.certification-icon {
  font-size: 2.5rem;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gold-gradient);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.certification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.certification-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  line-height: 1.3;
}

.certification-status {
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
  position: relative;
  z-index: 2;
}

.certification-status.completed {
  background: var(--success-gradient);
  box-shadow: var(--shadow-success);
  animation: completedPulse 2s ease-in-out infinite;
}

.certification-status.ongoing {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-md);
}

@keyframes completedPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.certification-progress {
  width: 120px;
  flex-shrink: 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6px;
  transition: all 1s ease-out;
  position: relative;
  overflow: hidden;
}

.progress-fill[data-status="completed"] {
  width: 100%;
  background: var(--success-gradient);
  animation: completedProgress 2s ease-out;
}

.progress-fill[data-status="ongoing"] {
  width: 75%;
  background: var(--primary-gradient);
  animation: ongoingProgress 2s ease-out;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes completedProgress {
  from { width: 0%; }
  to { width: 100%; }
}

@keyframes ongoingProgress {
  from { width: 0%; }
  to { width: 75%; }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@media (max-width: 768px) {
  .certification-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .certification-progress {
    width: 100%;
  }
}
