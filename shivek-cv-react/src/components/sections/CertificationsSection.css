.certifications-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.certification-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.certification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.certification-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
  flex-shrink: 0;
}

.certification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.certification-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  line-height: 1.3;
}

.certification-status {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.certification-progress {
  width: 120px;
  flex-shrink: 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 75%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  animation: progressAnimation 2s ease-out;
}

@keyframes progressAnimation {
  from {
    width: 0%;
  }
  to {
    width: 75%;
  }
}

@media (max-width: 768px) {
  .certification-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .certification-progress {
    width: 100%;
  }
}
