.languages-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.language-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.language-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.language-flag {
  font-size: 1.25rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 6px;
  flex-shrink: 0;
}

.language-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

@media (max-width: 768px) {
  .language-item {
    padding: 0.5rem;
  }
}
