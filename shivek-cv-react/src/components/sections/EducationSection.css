.education-timeline {
  position: relative;
  padding-left: 2rem;
}

.education-item {
  position: relative;
  margin-bottom: 2rem;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.education-item:last-child {
  margin-bottom: 0;
}

.education-item:last-child .marker-line {
  display: none;
}

.education-marker {
  position: relative;
  flex-shrink: 0;
}

.marker-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: 3px solid white;
  box-shadow: 0 0 0 3px #e5e7eb, 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 2;
}

.marker-line {
  position: absolute;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 60px;
  background: #e5e7eb;
  z-index: 1;
}

.education-content {
  flex: 1;
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.education-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.education-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.education-degree {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  line-height: 1.3;
}

.education-honors {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.education-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.education-institution,
.education-period {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  color: #4b5563;
}

.institution-icon,
.period-icon {
  font-size: 1.1rem;
}

.education-institution {
  font-weight: 600;
  color: #374151;
}

@media (max-width: 768px) {
  .education-timeline {
    padding-left: 1rem;
  }
  
  .education-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .education-details {
    gap: 0.5rem;
  }
}
