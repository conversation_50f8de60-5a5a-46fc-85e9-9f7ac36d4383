export const cvData = {
  profile: {
    name: "<PERSON><PERSON><PERSON>",
    title: "Automation & Software Engineer",
    email: "<EMAIL>",
    phone: "************ / ************",
    address: "65 Longcroft Drive, 4068",
    dateOfBirth: "24 July 2003",
    gender: "Male",
    nationality: "South African",
    linkedin: "linkedin.com/in/shivek-garib-52b00a285",
    summary: "Enthusiastic Automation & Software Engineer with hands-on experience in AI-driven solutions, full-stack development, and machine learning systems. Skilled in integrating advanced technologies like prompt engineering, tokenization, and automation pipelines across global teams. Proven contributor in fast-paced environments, with a passion for scalable, intelligent systems."
  },

  skills: {
    programming: ["React", "Python", "Node.js", "C++", "JavaScript", "PHP"],
    web: ["HTML", "CSS"],
    databases: ["SQL"],
    ai: ["Prompt Engineering", "Tokenization of AI", "Machine Learning", "Deep Learning"],
    tools: ["Microsoft Excel", "PowerPoint"],
    softSkills: ["Leadership", "Problem Solving", "Creative Thinking", "Software Testing", "Networking"]
  },

  education: [
    {
      degree: "Bachelor of Science in Information Technology",
      institution: "Richfield Graduate Institute of Technology",
      period: "Feb 2022 – 2024",
      honors: "Cum Laude"
    },
    {
      degree: "National Senior Certificate (NSC)",
      institution: "Earlington Secondary School",
      period: "Jan 2017 – Dec 2021"
    }
  ],

  experience: [
    {
      title: "Software Engineer",
      company: "OD International",
      period: "Sept 2024 – Present",
      responsibilities: [
        "Built automation tools and AI systems",
        "Collaborated with IBV AI teams in London, Switzerland, and Dubai",
        "Developed advanced AI workflows and prompt systems"
      ]
    },
    {
      title: "IT Manager",
      company: "Elysian Group",
      period: "Jun 2023 – Jan 2024",
      responsibilities: [
        "Managed IT infrastructure",
        "Software installation and troubleshooting",
        "System optimization"
      ]
    },
    {
      title: "IT Support",
      company: "SNL Distribution",
      period: "2021 – 2022 (Part-time during school holidays)",
      responsibilities: [
        "Maintained systems",
        "Assisted in networking and IT support"
      ]
    }
  ],

  projects: [
    {
      name: "IBV International Vaults Wealth Club App",
      type: "Full-stack development",
      description: "Comprehensive wealth management application with modern architecture"
    },
    {
      name: "AI Tutor",
      type: "NLP-based AI tutoring platform",
      description: "Intelligent tutoring system using natural language processing"
    },
    {
      name: "E-Learning Platform",
      type: "Scalable online learning system",
      description: "Modern educational platform with scalable architecture"
    },
    {
      name: "Automated Excel File Retrieval System",
      type: "Automated workflow solution",
      description: "Automated system for efficient file management and retrieval"
    }
  ],

  certifications: [
    {
      name: "AWS Cloud Practitioner",
      status: "Ongoing"
    },
    {
      name: "Microsoft Azure",
      status: "Ongoing"
    },
    {
      name: "Introduction to Hacking",
      status: "Ongoing"
    }
  ],

  references: [
    {
      name: "Reshmee Sookhoo",
      company: "Elysian Group",
      phone: "************"
    },
    {
      name: "Adil",
      company: "SNL Distribution",
      phone: "************",
      email: "<EMAIL>"
    }
  ],

  hobbies: ["Coding", "Hacking", "Calisthenics", "Football"],

  languages: ["English", "Hindi", "Afrikaans"]
};
